<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI赛车强化训练器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #ecf0f1;
            min-height: 100vh;
            overflow: hidden;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 350px;
            grid-gap: 20px;
        }
        
        header {
            grid-column: 1 / -1;
            text-align: center;
            padding: 20px 0;
            border-bottom: 3px solid #3498db;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            background: linear-gradient(to right, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .subtitle {
            color: #bdc3c7;
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .game-area {
            background: rgba(0, 15, 30, 0.85);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }
        
        canvas {
            background: #0a1929;
            border-radius: 10px;
            display: block;
            margin: 0 auto;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        button {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        button.reset {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        
        button.train {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        
        .sidebar {
            background: rgba(0, 15, 30, 0.85);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
        }
        
        .panel {
            background: rgba(10, 30, 50, 0.7);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .panel h2 {
            color: #3498db;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            font-size: 1.5rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: rgba(0, 40, 80, 0.5);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin: 10px 0;
            color: #2ecc71;
        }
        
        .stat-label {
            color: #bdc3c7;
            font-size: 0.9rem;
        }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #bdc3c7;
        }
        
        .progress-bar {
            height: 25px;
            background: rgba(0, 30, 60, 0.8);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 12px;
            background: linear-gradient(to right, #3498db, #2ecc71);
            width: 65%;
            transition: width 0.5s ease;
        }
        
        .neural-viz {
            height: 200px;
            background: rgba(0, 20, 40, 0.7);
            border-radius: 10px;
            margin-top: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .neuron {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #3498db;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 15px rgba(52, 152, 219, 0.8);
        }
        
        .connection {
            position: absolute;
            height: 2px;
            background: rgba(236, 240, 241, 0.2);
            transform-origin: 0 0;
        }
        
        .road-gen-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-top: 15px;
        }
        
        select, input {
            background: rgba(0, 30, 60, 0.8);
            color: white;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 10px;
            font-size: 1rem;
        }
        
        .training-log {
            flex-grow: 1;
            background: rgba(0, 20, 40, 0.7);
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            color: #2ecc71;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-time {
            color: #3498db;
            margin-right: 10px;
        }
        
        .ai-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }
        
        .ai-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #e74c3c;
            margin-right: 10px;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.8);
        }
        
        .ai-indicator.active {
            background: #2ecc71;
            box-shadow: 0 0 15px rgba(46, 204, 113, 0.8);
        }
        
        footer {
            grid-column: 1 / -1;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 1100px) {
            .container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                max-height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI 赛车强化训练器</h1>
            <p class="subtitle">使用神经网络和强化学习训练自动驾驶赛车在随机生成的连续道路上高效行驶</p>
        </header>
        
        <main class="game-area">
            <canvas id="gameCanvas" width="800" height="500"></canvas>
            <div class="controls">
                <button id="startBtn">开始训练</button>
                <button id="resetBtn" class="reset">重置</button>
                <button id="demoBtn">演示模式</button>
                <button id="trainBtn" class="train">训练一代</button>
            </div>
        </main>
        
        <aside class="sidebar">
            <div class="panel">
                <h2>训练统计</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-label">当前代数</div>
                        <div class="stat-value" id="genCount">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">最高分数</div>
                        <div class="stat-value" id="highScore">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">平均分数</div>
                        <div class="stat-value" id="avgScore">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">训练轮次</div>
                        <div class="stat-value" id="trainCount">0</div>
                    </div>
                </div>
                
                <div class="progress-container">
                    <div class="progress-label">
                        <span>训练进度</span>
                        <span id="progressText">65%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="trainProgress"></div>
                    </div>
                </div>
                
                <div class="ai-status">
                    <div class="ai-indicator" id="aiIndicator"></div>
                    <span id="aiStatusText">AI 状态: 等待训练</span>
                </div>
            </div>
            
            <div class="panel">
                <h2>神经网络可视化</h2>
                <div class="neural-viz" id="neuralViz">
                    <!-- 神经网络将通过JS动态生成 -->
                </div>
            </div>
            
            <div class="panel">
                <h2>道路生成设置</h2>
                <div class="road-gen-controls">
                    <label for="roadComplexity">道路复杂度</label>
                    <select id="roadComplexity">
                        <option value="low">低</option>
                        <option value="medium" selected>中等</option>
                        <option value="high">高</option>
                    </select>
                    
                    <label for="curveFrequency">弯道频率</label>
                    <select id="curveFrequency">
                        <option value="low">低</option>
                        <option value="medium" selected>中等</option>
                        <option value="high">高</option>
                    </select>
                    
                    <label for="roadWidth">道路宽度</label>
                    <select id="roadWidth">
                        <option value="narrow">窄</option>
                        <option value="medium" selected>中等</option>
                        <option value="wide">宽</option>
                    </select>
                </div>
            </div>
            
            <div class="panel">
                <h2>训练日志</h2>
                <div class="training-log" id="trainingLog">
                    <div class="log-entry"><span class="log-time">[00:00:00]</span> 系统初始化完成</div>
                    <div class="log-entry"><span class="log-time">[00:00:02]</span> 神经网络加载成功</div>
                    <div class="log-entry"><span class="log-time">[00:00:05]</span> 等待开始训练...</div>
                </div>
            </div>
        </aside>
        
        <footer>
            <p>AI赛车强化训练器 | 使用HTML5 Canvas和TensorFlow.js | 强化学习演示</p>
        </footer>
    </div>

    <script>
        // 游戏主类
        class RacingGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.cars = [];
                this.road = [];
                this.genCount = 0;
                this.trainCount = 0;
                this.highScore = 0;
                this.avgScore = 0;
                this.isTraining = false;
                this.trainingProgress = 0;
                this.lastUpdateTime = 0;
                this.fps = 60;
                this.roadGenerationCooldown = 0;
                this.init();
            }
            
            init() {
                this.createRoad();
                this.createInitialCars();
                this.renderNeuralNetwork();
                this.setupEventListeners();
                this.logEvent("游戏初始化完成");
            }
            
            createRoad() {
                // 生成初始道路
                this.road = [];
                const segmentCount = 100;
                const roadWidth = 300;
                
                for (let i = 0; i < segmentCount; i++) {
                    const x = (i / segmentCount) * this.canvas.width * 3;
                    const y = this.canvas.height / 2 + (Math.sin(i * 0.05) * 100);
                    const width = roadWidth - (i * 0.5);
                    this.road.push({x, y, width});
                }
            }
            
            generateNewRoadSegment() {
                // 生成新的道路段
                if (this.road.length > 500) return; // 防止无限增长
                
                const lastSegment = this.road[this.road.length - 1];
                const newX = lastSegment.x + 100;
                
                // 根据道路复杂度生成弯道
                const curveIntensity = 0.02;
                let curveDirection = 1;
                if (Math.random() > 0.5) curveDirection = -1;
                
                // 添加一些随机性
                const curveAmount = (Math.random() - 0.5) * curveIntensity * 2;
                const newY = lastSegment.y + curveDirection * curveAmount * 100;
                
                // 根据道路宽度调整
                const widthReduction = 0.2 + Math.random() * 0.3;
                const newWidth = lastSegment.width * (1 - widthReduction);
                
                this.road.push({x: newX, y: newY, width: newWidth});
            }
            
            createInitialCars() {
                // 创建AI赛车
                for (let i = 0; i < 8; i++) {
                    const car = {
                        x: 100,
                        y: this.canvas.height / 2 + (i - 3) * 40,
                        width: 40,
                        height: 20,
                        speed: 3 + Math.random() * 2,
                        angle: 0,
                        color: this.getRandomColor(),
                        score: 0,
                        distance: 0,
                        neuralNet: this.createNeuralNetwork(),
                        isCrashed: false
                    };
                    this.cars.push(car);
                }
            }
            
            createNeuralNetwork() {
                // 创建一个简单的神经网络表示
                return {
                    layers: [
                        { neurons: 5, weights: [] }, // 输入层
                        { neurons: 8, weights: [] }, // 隐藏层
                        { neurons: 3, weights: [] }  // 输出层
                    ]
                };
            }
            
            getRandomColor() {
                const colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f', '#9b59b6', '#1abc9c', '#d35400', '#34495e'];
                return colors[Math.floor(Math.random() * colors.length)];
            }
            
            update(deltaTime) {
                // 更新游戏状态
                this.roadGenerationCooldown -= deltaTime;
                if (this.roadGenerationCooldown <= 0) {
                    this.generateNewRoadSegment();
                    this.roadGenerationCooldown = 100; // 每100ms生成新段
                }
                
                // 更新赛车
                this.cars.forEach(car => {
                    if (!car.isCrashed) {
                        // 简单的AI决策（实际应用中将使用神经网络）
                        this.updateCarAI(car);
                        
                        // 更新位置
                        car.distance += car.speed;
                        car.x += Math.sin(car.angle) * car.speed;
                        car.y += Math.cos(car.angle) * car.speed;
                        
                        // 检查碰撞
                        this.checkCollision(car);
                        
                        // 更新得分
                        car.score = car.distance / 10;
                    }
                });
                
                // 更新训练统计
                this.updateTrainingStats();
                
                // 渲染
                this.render();
            }
            
            updateCarAI(car) {
                // 简单的AI逻辑（实际将替换为神经网络决策）
                // 1. 获取前方道路信息
                const lookAhead = 150;
                const lookX = car.x + Math.sin(car.angle) * lookAhead;
                const lookY = car.y + Math.cos(car.angle) * lookAhead;
                
                // 2. 检测道路边界
                let leftDist = 0;
                let rightDist = 0;
                
                // 简化版边界检测
                if (car.x < this.road[car.distance/10|0].x - this.road[car.distance/10|0].width/2) {
                    leftDist = (this.road[car.distance/10|0].x - this.road[car.distance/10|0].width/2) - car.x;
                }
                
                if (car.x > this.road[car.distance/10|0].x + this.road[car.distance/10|0].width/2) {
                    rightDist = car.x - (this.road[car.distance/10|0].x + this.road[car.distance/10|0].width/2);
                }
                
                // 3. 简单决策：转向有更多空间的方向
                if (leftDist > rightDist + 20) {
                    car.angle -= 0.05;
                } else if (rightDist > leftDist + 20) {
                    car.angle += 0.05;
                }
                
                // 随机调整速度以模拟探索
                if (Math.random() > 0.95) {
                    car.speed